#!/bin/bash

# CSR 应用清理版本部署脚本
# 用于部署 v1.4.0 清理版本，自动清理旧 Service Worker 并迁移用户到 Nuxt 应用

set -e

echo "🚀 开始部署 CSR 应用清理版本 v1.4.0..."

# 检查当前目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误：请在 csr-app 目录下运行此脚本"
    exit 1
fi

# 检查版本号
CURRENT_VERSION=$(node -p "require('./package.json').version")
if [ "$CURRENT_VERSION" != "1.4.0" ]; then
    echo "❌ 错误：package.json 版本号不是 1.4.0，当前版本：$CURRENT_VERSION"
    exit 1
fi

# 检查 sw-advanced.js 版本号
if ! grep -q "playshot-v1.4.0" public/sw-advanced.js; then
    echo "❌ 错误：sw-advanced.js 中未找到 v1.4.0 版本号"
    exit 1
fi

echo "✅ 版本检查通过：v$CURRENT_VERSION"

# 选择构建环境
echo ""
echo "请选择部署环境："
echo "1) ReelPlay 生产环境 (prod.reelplay)"
echo "2) PlayShot 生产环境 (prod.playshot)"
echo "3) 预发布环境 (pre)"
echo ""
read -p "请输入选择 (1-3): " choice

case $choice in
    1)
        BUILD_ENV="prod.reelplay"
        echo "🎯 选择环境：ReelPlay 生产环境"
        ;;
    2)
        BUILD_ENV="prod.playshot"
        echo "🎯 选择环境：PlayShot 生产环境"
        ;;
    3)
        BUILD_ENV="pre"
        echo "🎯 选择环境：预发布环境"
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "🔧 开始构建..."

# 清理旧的构建文件
if [ -d "dist" ]; then
    rm -rf dist
    echo "🗑️ 清理旧构建文件"
fi

# 执行构建
if [ "$BUILD_ENV" = "prod.reelplay" ]; then
    npm run build:prod:reelplay
elif [ "$BUILD_ENV" = "prod.playshot" ]; then
    npm run build:prod:playshot
else
    npm run build:pre
fi

echo "✅ 构建完成"

# 检查构建结果
if [ ! -d "dist" ]; then
    echo "❌ 构建失败：dist 目录不存在"
    exit 1
fi

if [ ! -f "dist/sw-advanced.js" ]; then
    echo "❌ 构建失败：sw-advanced.js 未找到"
    exit 1
fi

if [ ! -f "dist/version.json" ]; then
    echo "❌ 构建失败：version.json 未找到"
    exit 1
fi

# 验证构建的 Service Worker
if ! grep -q "v1.4.0" dist/sw-advanced.js; then
    echo "❌ 构建失败：构建的 sw-advanced.js 版本号不正确"
    exit 1
fi

echo "✅ 构建验证通过"

# 显示部署信息
echo ""
echo "🎉 清理版本构建完成！"
echo ""
echo "📋 部署信息："
echo "   版本：v1.4.0"
echo "   环境：$BUILD_ENV"
echo "   构建目录：./dist"
echo ""
echo "🔧 清理功能："
echo "   ✓ 自动清理所有旧版本缓存 (v1.3.1)"
echo "   ✓ 清理 VitePWA 生成的缓存"
echo "   ✓ 清理相关 localStorage"
echo "   ✓ 自动重定向 /landing 到 Nuxt 应用"
echo "   ✓ 强制重新加载所有打开的页面"
echo ""
echo "📝 部署后效果："
echo "   1. 用户访问时会自动更新到 v1.4.0"
echo "   2. 所有旧缓存会被清理"
echo "   3. /landing 页面会自动跳转到 https://reelplay.ai/landing"
echo "   4. 其他页面会重新加载以使用最新版本"
echo ""
echo "⚠️  注意事项："
echo "   - 请确保 https://reelplay.ai 的 Nuxt 应用已正常部署"
echo "   - 建议在低峰期部署以减少用户影响"
echo "   - 部署后可通过浏览器开发者工具监控 SW 更新过程"
echo ""

# 询问是否继续部署
read -p "是否继续部署到服务器？(y/N): " deploy_confirm

if [[ $deploy_confirm =~ ^[Yy]$ ]]; then
    echo ""
    echo "📤 请手动将 dist 目录内容部署到对应的服务器"
    echo "   或使用您的 CI/CD 流程进行部署"
    echo ""
    echo "🔍 部署后验证步骤："
    echo "   1. 访问应用首页，检查控制台是否显示 'SW: Activating v1.4.0'"
    echo "   2. 访问 /landing 页面，确认是否重定向到 Nuxt 应用"
    echo "   3. 检查 Application > Storage > Cache Storage 是否清理了旧缓存"
    echo "   4. 检查 localStorage 是否清理了相关项目"
else
    echo "📁 构建文件已准备就绪，位于 ./dist 目录"
fi

echo ""
echo "🎯 部署完成后，用户将自动迁移到 Nuxt 应用！"
