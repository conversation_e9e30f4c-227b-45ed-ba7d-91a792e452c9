#!/usr/bin/env node

/**
 * 验证清理版本的配置是否正确
 */

import fs from 'fs'
import path from 'path'

console.log('🔍 验证 CSR 应用清理版本配置...\n')

let hasErrors = false

// 1. 检查 package.json 版本
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
  if (packageJson.version === '1.4.0') {
    console.log('✅ package.json 版本正确：v' + packageJson.version)
  } else {
    console.log(
      '❌ package.json 版本错误：v' + packageJson.version + '，应该是 v1.4.0',
    )
    hasErrors = true
  }
} catch (error) {
  console.log('❌ 无法读取 package.json：' + error.message)
  hasErrors = true
}

// 2. 检查 sw-advanced.js 版本号
try {
  const swContent = fs.readFileSync('public/sw-advanced.js', 'utf8')

  const versionChecks = [
    { pattern: /playshot-v1\.4\.0/g, name: 'CACHE_NAME' },
    { pattern: /playshot-static-v1\.4\.0/g, name: 'STATIC_CACHE' },
    { pattern: /playshot-dynamic-v1\.4\.0/g, name: 'DYNAMIC_CACHE' },
    { pattern: /playshot-cdn-v1\.4\.0/g, name: 'CDN_CACHE' },
  ]

  versionChecks.forEach((check) => {
    if (check.pattern.test(swContent)) {
      console.log(`✅ sw-advanced.js ${check.name} 版本正确`)
    } else {
      console.log(`❌ sw-advanced.js ${check.name} 版本错误，应包含 v1.4.0`)
      hasErrors = true
    }
  })

  // 检查是否包含清理逻辑
  if (swContent.includes('SW: Activating v1.4.0')) {
    console.log('✅ sw-advanced.js 包含 v1.4.0 激活日志')
  } else {
    console.log('❌ sw-advanced.js 缺少 v1.4.0 激活日志')
    hasErrors = true
  }

  if (swContent.includes('清理旧缓存')) {
    console.log('✅ sw-advanced.js 包含缓存清理逻辑')
  } else {
    console.log('❌ sw-advanced.js 缺少缓存清理逻辑')
    hasErrors = true
  }

  if (swContent.includes('迁移到 Nuxt 应用')) {
    console.log('✅ sw-advanced.js 包含迁移逻辑')
  } else {
    console.log('❌ sw-advanced.js 缺少迁移逻辑')
    hasErrors = true
  }

  if (swContent.includes('https://reelplay.ai')) {
    console.log('✅ sw-advanced.js 包含正确的重定向目标')
  } else {
    console.log('❌ sw-advanced.js 缺少重定向目标或目标错误')
    hasErrors = true
  }
} catch (error) {
  console.log('❌ 无法读取 sw-advanced.js：' + error.message)
  hasErrors = true
}

// 3. 检查 main.ts 清理逻辑
try {
  const mainContent = fs.readFileSync('src/main.ts', 'utf8')

  if (mainContent.includes('v1.4.0 清理逻辑')) {
    console.log('✅ main.ts 包含 localStorage 清理逻辑')
  } else {
    console.log('❌ main.ts 缺少 localStorage 清理逻辑')
    hasErrors = true
  }

  if (mainContent.includes('sw_cleanup_version')) {
    console.log('✅ main.ts 包含清理版本标记')
  } else {
    console.log('❌ main.ts 缺少清理版本标记')
    hasErrors = true
  }
} catch (error) {
  console.log('❌ 无法读取 src/main.ts：' + error.message)
  hasErrors = true
}

// 4. 检查部署脚本
if (fs.existsSync('deploy-cleanup.sh')) {
  console.log('✅ 部署脚本存在')

  try {
    const stats = fs.statSync('deploy-cleanup.sh')
    if (stats.mode & parseInt('111', 8)) {
      console.log('✅ 部署脚本有执行权限')
    } else {
      console.log(
        '⚠️  部署脚本没有执行权限，请运行：chmod +x deploy-cleanup.sh',
      )
    }
  } catch (error) {
    console.log('⚠️  无法检查部署脚本权限：' + error.message)
  }
} else {
  console.log('❌ 部署脚本不存在')
  hasErrors = true
}

console.log('\n' + '='.repeat(50))

if (hasErrors) {
  console.log('❌ 验证失败！请修复上述错误后重新验证。')
  process.exit(1)
} else {
  console.log('🎉 验证通过！清理版本配置正确。')
  console.log('\n📋 配置摘要：')
  console.log('   • 版本：v1.4.0')
  console.log('   • Service Worker 清理：已配置')
  console.log('   • localStorage 清理：已配置')
  console.log('   • 迁移重定向：已配置')
  console.log('   • 部署脚本：已准备')
  console.log('\n🚀 可以开始部署了！运行：./deploy-cleanup.sh')
}
